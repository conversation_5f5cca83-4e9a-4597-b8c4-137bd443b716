import { mastra } from '../../agent';

export async function getWordDefinition(word: string): Promise<string> {
  try {
    const agent = mastra.getAgent('dictionaryAgent');
    const response = await agent.generate([
      {
        role: 'user',
        content: `Define the word "${word}" using the smithery_get_definitions MCP tool. You must use the tool, do not provide definitions from your own knowledge. Respond in English.`,
      }
    ]);

    return response.text;
  } catch (error) {
    console.error("Error getting definition:", error);
    return `Error getting definition for "${word}". Please try again.`;
  }
}