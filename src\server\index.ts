import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import { mastra } from '../mastra/index.js';

dotenv.config();

const app = express();
const PORT = process.env.PORT || 3003;

// Middleware
app.use(cors({
  origin: 'http://localhost:3000', // React app URL
  credentials: true
}));
app.use(express.json());

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Mastra Agent Server is running' });
});

// Dictionary API endpoint
app.post('/api/dictionary', async (req, res) => {
  try {
    const { word } = req.body;

    if (!word || typeof word !== 'string') {
      return res.status(400).json({
        error: 'Word parameter is required and must be a string'
      });
    }

    console.log(`🔍 Getting definition for: ${word}`);

    const agent = mastra.getAgent('dictionaryAgent');
    const response = await agent.generate([
      {
        role: 'user',
        content: `Define the word "${word}" using the smithery_get_definitions MCP tool. You must use the tool, do not provide definitions from your own knowledge. Respond in English.`,
      }
    ]);

    console.log(`✅ Definition found for: ${word}`);

    res.json({
      word,
      definition: response.text,
      success: true
    });

  } catch (error) {
    console.error('❌ Error getting definition:', error);
    res.status(500).json({
      error: 'Failed to get word definition',
      message: error instanceof Error ? error.message : 'Unknown error',
      success: false
    });
  }
});

// Error handling middleware
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Server error:', err);
  res.status(500).json({
    error: 'Internal server error',
    message: err.message
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Mastra Agent Server running on http://localhost:${PORT}`);
  console.log(`📚 Dictionary API available at http://localhost:${PORT}/api/dictionary`);
  console.log(`🏥 Health check at http://localhost:${PORT}/health`);
});

export default app;
