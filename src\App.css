.App {
  text-align: center;
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  font-size: calc(10px + 1vmin);
  color: white;
}

form {
  display: flex;
  margin: 20px 0;
  width: 100%;
  max-width: 500px;
}

input {
  flex: 1;
  padding: 10px;
  font-size: 16px;
  border: none;
  border-radius: 4px 0 0 4px;
}

button {
  padding: 10px 20px;
  background-color: #61dafb;
  color: #282c34;
  border: none;
  border-radius: 0 4px 4px 0;
  font-size: 16px;
  cursor: pointer;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.definition-container {
  margin-top: 20px;
  background-color: #3a3f4b;
  padding: 20px;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  text-align: left;
}

.definition-text {
  white-space: pre-wrap;
}