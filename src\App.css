.App {
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.App-header {
  padding: 40px 20px;
  color: white;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 30px;
}

.App-header h1 {
  font-size: 3rem;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

.search-form {
  width: 100%;
  max-width: 600px;
}

.input-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}

.word-input {
  flex: 1;
  min-width: 250px;
  padding: 15px 20px;
  font-size: 1.1rem;
  border: none;
  border-radius: 25px;
  outline: none;
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.word-input:focus {
  box-shadow: 0 6px 20px rgba(0,0,0,0.2);
  transform: translateY(-2px);
}

.word-input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.search-button, .clear-button {
  padding: 15px 25px;
  font-size: 1rem;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
  min-width: 120px;
}

.search-button {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  box-shadow: 0 4px 15px rgba(255,107,107,0.3);
}

.search-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255,107,107,0.4);
}

.search-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.clear-button {
  background: linear-gradient(45deg, #74b9ff, #0984e3);
  color: white;
  box-shadow: 0 4px 15px rgba(116,185,255,0.3);
}

.clear-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(116,185,255,0.4);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  margin: 20px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255,255,255,0.3);
  border-top: 4px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.definition-container {
  background: rgba(255,255,255,0.95);
  color: #333;
  padding: 30px;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  max-width: 800px;
  margin: 20px auto;
  animation: fadeInUp 0.5s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.definition-container h2 {
  color: #2d3436;
  margin-bottom: 20px;
  font-size: 1.5rem;
}

.definition-text {
  font-size: 1.1rem;
  line-height: 1.6;
  text-align: left;
  color: #2d3436;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #667eea;
  white-space: pre-wrap;
}

.app-footer {
  margin-top: auto;
  padding: 20px;
  opacity: 0.7;
  font-size: 0.9rem;
}

.app-footer p {
  margin: 0;
}

@media (max-width: 768px) {
  .App-header h1 {
    font-size: 2rem;
  }

  .input-group {
    flex-direction: column;
    width: 100%;
  }

  .word-input {
    min-width: auto;
    width: 100%;
  }

  .search-button, .clear-button {
    width: 100%;
  }

  .definition-container {
    margin: 20px 10px;
    padding: 20px;
  }
}