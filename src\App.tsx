import React, { useState } from 'react';
import { getWordDefinition } from './api/dictionaryApi';
import './App.css';

function App() {
  const [word, setWord] = useState('');
  const [definition, setDefinition] = useState('');
  const [loading, setLoading] = useState(false);
  const [searchedWord, setSearchedWord] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!word.trim()) return;

    setLoading(true);
    setSearchedWord(word);
    setDefinition('');

    try {
      const result = await getWordDefinition(word.trim());
      setDefinition(result);
    } catch (error) {
      console.error('Error:', error);
      setDefinition('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleClear = () => {
    setWord('');
    setDefinition('');
    setSearchedWord('');
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>🤖 Mastra Dictionary Agent</h1>
        <p className="subtitle">AI-powered word definitions using MCP tools</p>

        <form onSubmit={handleSubmit} className="search-form">
          <div className="input-group">
            <input
              type="text"
              value={word}
              onChange={(e) => setWord(e.target.value)}
              placeholder="Enter a word to define..."
              disabled={loading}
              className="word-input"
            />
            <button
              type="submit"
              disabled={loading || !word.trim()}
              className="search-button"
            >
              {loading ? '🔍 Searching...' : '🔍 Search'}
            </button>
            {(word || definition) && (
              <button
                type="button"
                onClick={handleClear}
                className="clear-button"
                disabled={loading}
              >
                ✖ Clear
              </button>
            )}
          </div>
        </form>

        {loading && (
          <div className="loading-container">
            <div className="spinner"></div>
            <p>Agent is thinking...</p>
          </div>
        )}

        {definition && !loading && (
          <div className="definition-container">
            <h2>📖 Definition for: "{searchedWord}"</h2>
            <div className="definition-text">
              {definition}
            </div>
          </div>
        )}

        <footer className="app-footer">
          <p>Powered by Mastra Agent Framework</p>
        </footer>
      </header>
    </div>
  );
}

export default App;