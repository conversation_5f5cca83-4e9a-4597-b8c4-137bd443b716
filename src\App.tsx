import { useState } from 'react';
import { getWordDefinition } from './api/dictionaryApi';
import './App.css';

function App() {
  const [word, setWord] = useState('');
  const [definition, setDefinition] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!word.trim()) return;
    
    setLoading(true);
    try {
      const result = await getWordDefinition(word);
      setDefinition(result);
    } catch (error) {
      console.error('Error:', error);
      setDefinition('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>Mastra Dictionary</h1>
        <form onSubmit={handleSubmit}>
          <input
            type="text"
            value={word}
            onChange={(e) => setWord(e.target.value)}
            placeholder="Enter a word..."
            disabled={loading}
          />
          <button type="submit" disabled={loading || !word.trim()}>
            {loading ? 'Searching...' : 'Search'}
          </button>
        </form>
        
        {definition && (
          <div className="definition-container">
            <h2>Definition for: {word}</h2>
            <div className="definition-text">
              {definition}
            </div>
          </div>
        )}
      </header>
    </div>
  );
}

export default App;