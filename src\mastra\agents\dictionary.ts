import { Agent } from '@mastra/core';
import { openai } from '@ai-sdk/openai';
import { mcp } from '../mcp.js';
import { getDefinitions } from '../tools/dictionary.js';

// OpenAI model yapılandırması
const model = openai('gpt-4o-mini');

// MCP tools'ları al ve agent'ı oluştur
async function createDictionaryAgent() {
  try {
    console.log('🔧 MCP tools alınıyor...');
    const mcpTools = await mcp.getTools();
    console.log('🛠️ MCP tools:', Object.keys(mcpTools));

    return new Agent({
      name: 'dictionaryAgent',
      instructions: `You are a dictionary assistant. You MUST use tools to get word definitions.

      CRITICAL RULES:
      1. When asked about a word, you MUST call the "smithery_get_definitions" tool with the word parameter
      2. NEVER provide definitions from your own knowledge - ONLY use tool results
      3. If smithery_get_definitions fails, then call "getDefinitions" as fallback
      4. Always respond in English
      5. Present the definitions clearly and in a numbered list
      6. Always call a tool - never respond without using a tool first

      Available Tools:
      - smithery_get_definitions: Real MCP server dictionary tool (USE THIS FIRST)
      - getDefinitions: Fallback dictionary tool

      EXAMPLE WORKFLOW:
      User: "Define water"
      You: Call smithery_get_definitions with parameter {word: "water"}
      Then: Present the results in a numbered list

      REMEMBER: You must ALWAYS call a tool for word definitions. Never use your own knowledge.`,
      model,
      tools: {
        getDefinitions,
        ...mcpTools, // MCP tools'ları ekle
      },
    });
  } catch (error) {
    console.log('⚠️ MCP tools alınamadı, fallback agent oluşturuluyor:', error);

    return new Agent({
      name: 'dictionaryAgent',
      instructions: `You are a dictionary assistant. You MUST use tools to get word definitions.

      CRITICAL RULES:
      1. When asked about a word, you MUST call the "getDefinitions" tool with the word parameter
      2. NEVER provide definitions from your own knowledge - ONLY use tool results
      3. Always respond in English
      4. Present the definitions clearly and in a numbered list
      5. Always call a tool - never respond without using a tool first

      Available Tools:
      - getDefinitions: Dictionary tool for word definitions

      EXAMPLE WORKFLOW:
      User: "Define water"
      You: Call getDefinitions with parameter {word: "water"}
      Then: Present the results in a numbered list

      REMEMBER: You must ALWAYS call a tool for word definitions. Never use your own knowledge.`,
      model,
      tools: {
        getDefinitions,
      },
    });
  }
}

export const dictionaryAgent = await createDictionaryAgent();
